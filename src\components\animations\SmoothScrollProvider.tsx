"use client";

import { useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface SmoothScrollProviderProps {
  children: React.ReactNode;
}

export default function SmoothScrollProvider({ children }: SmoothScrollProviderProps) {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Clean up any existing ScrollTrigger instances
    ScrollTrigger.getAll().forEach(trigger => trigger.kill());

    // Initialize scroll animations without interfering with scroll behavior
    let ctx = gsap.context(() => {
      // Subtle parallax effects
      gsap.utils.toArray('.parallax-element').forEach((element: any) => {
        gsap.to(element, {
          yPercent: -15,
          ease: "none",
          scrollTrigger: {
            trigger: element,
            start: "top bottom",
            end: "bottom top",
            scrub: 1,
            invalidateOnRefresh: true
          }
        });
      });

      // Fade in animations
      gsap.utils.toArray('.animate-fade-in').forEach((element: any) => {
        gsap.fromTo(element,
          {
            opacity: 0,
            y: 30
          },
          {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: "power2.out",
            scrollTrigger: {
              trigger: element,
              start: "top 85%",
              toggleActions: "play none none reverse"
            }
          }
        );
      });

      // Scale animations
      gsap.utils.toArray('.animate-scale-in').forEach((element: any) => {
        gsap.fromTo(element,
          {
            scale: 0.95,
            opacity: 0
          },
          {
            scale: 1,
            opacity: 1,
            duration: 0.6,
            ease: "power2.out",
            scrollTrigger: {
              trigger: element,
              start: "top 85%",
              toggleActions: "play none none reverse"
            }
          }
        );
      });

      // Slide up animations
      gsap.utils.toArray('.animate-slide-up').forEach((element: any) => {
        gsap.fromTo(element,
          {
            y: 40,
            opacity: 0
          },
          {
            y: 0,
            opacity: 1,
            duration: 0.7,
            ease: "power2.out",
            scrollTrigger: {
              trigger: element,
              start: "top 85%",
              toggleActions: "play none none reverse"
            }
          }
        );
      });

    });

    return () => {
      ctx.revert();
      ScrollTrigger.refresh();
    };
  }, []);

  return <>{children}</>;
}
