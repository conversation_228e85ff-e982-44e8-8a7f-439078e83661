"use client";

import { useState, useEffect, useRef } from 'react';

interface Skill {
  name: string;
  level: number;
  category: 'frontend' | 'backend' | 'tools' | 'other';
}

const SkillShowcase = () => {
  const [visibleSkills, setVisibleSkills] = useState<Set<number>>(new Set());
  const skillRefs = useRef<(HTMLDivElement | null)[]>([]);

  const skills: Skill[] = [
    { name: 'React', level: 95, category: 'frontend' },
    { name: 'TypeScript', level: 90, category: 'frontend' },
    { name: 'Next.js', level: 88, category: 'frontend' },
    { name: 'Node.js', level: 85, category: 'backend' },
    { name: 'Python', level: 80, category: 'backend' },
    { name: 'PostgreSQL', level: 75, category: 'backend' },
    { name: 'Docker', level: 70, category: 'tools' },
    { name: 'AWS', level: 72, category: 'tools' },
    { name: 'MongoDB', level: 78, category: 'backend' },
    { name: 'Rust', level: 65, category: 'other' }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const index = parseInt(entry.target.getAttribute('data-index') || '0');
          if (entry.isIntersecting) {
            setTimeout(() => {
              setVisibleSkills(prev => new Set([...prev, index]));
            }, index * 100); // Stagger the animations
          }
        });
      },
      { threshold: 0.3 }
    );

    skillRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => observer.disconnect();
  }, []);

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'frontend': return 'from-blue-500 to-cyan-500';
      case 'backend': return 'from-green-500 to-emerald-500';
      case 'tools': return 'from-purple-500 to-violet-500';
      case 'other': return 'from-orange-500 to-red-500';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {skills.map((skill, index) => (
        <div
          key={skill.name}
          ref={(el) => { skillRefs.current[index] = el; }}
          data-index={index}
          className="group"
        >
          <div className="flex justify-between items-center mb-2">
            <span className="font-medium text-deep-charcoal dark:text-dark-text">
              {skill.name}
            </span>
            <span className="text-sm text-deep-charcoal/70 dark:text-dark-text/70">
              {skill.level}%
            </span>
          </div>
          
          <div className="relative h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
            <div
              className={`
                absolute top-0 left-0 h-full rounded-full transition-all duration-1000 ease-out
                bg-gradient-to-r ${getCategoryColor(skill.category)}
                ${visibleSkills.has(index) ? 'opacity-100' : 'opacity-0'}
              `}
              style={{
                width: visibleSkills.has(index) ? `${skill.level}%` : '0%',
                transitionDelay: `${index * 100}ms`
              }}
            />
            
            {/* Shine effect */}
            <div
              className={`
                absolute top-0 left-0 h-full w-full rounded-full
                bg-gradient-to-r from-transparent via-white/20 to-transparent
                transform -skew-x-12 transition-transform duration-1000
                ${visibleSkills.has(index) ? 'translate-x-full' : '-translate-x-full'}
              `}
              style={{ transitionDelay: `${index * 100 + 500}ms` }}
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default SkillShowcase;
