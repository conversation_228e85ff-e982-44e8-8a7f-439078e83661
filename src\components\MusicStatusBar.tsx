"use client";

import { useState, useEffect } from 'react';
import { Music, Pause, Play, Volume2, VolumeX, Minimize2, Maximize2 } from 'lucide-react';
import { useMusicContext } from './MusicContext';

export default function MusicStatusBar() {
  const { isPlaying, currentTrack, volume, setVolume, musicIntensity } = useMusicContext();
  const [isMinimized, setIsMinimized] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  // Show the status bar when music starts playing
  useEffect(() => {
    if (isPlaying && !isVisible) {
      setIsVisible(true);
    }
  }, [isPlaying, isVisible]);

  // Auto-minimize after some time
  useEffect(() => {
    if (isPlaying && isVisible && !isMinimized) {
      const timer = setTimeout(() => {
        setIsMinimized(true);
      }, 10000); // Auto-minimize after 10 seconds
      return () => clearTimeout(timer);
    }
  }, [isPlaying, isVisible, isMinimized]);

  if (!isVisible) return null;

  const getIntensityBars = () => {
    const intensity = musicIntensity || 1;
    const barCount = Math.min(Math.floor(intensity * 5), 8);
    return Array.from({ length: 8 }, (_, i) => (
      <div
        key={i}
        className={`w-1 bg-accent-green rounded-full transition-all duration-200 ${
          i < barCount ? 'opacity-100' : 'opacity-30'
        }`}
        style={{
          height: `${Math.min(4 + i * 2 + (i < barCount ? Math.random() * 8 : 0), 24)}px`,
          animationDelay: `${i * 0.1}s`
        }}
      />
    ));
  };

  return (
    <div className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-50 transition-all duration-500 ${
      isMinimized ? 'scale-75 opacity-80' : 'scale-100 opacity-100'
    }`}>
      <div className="bg-white/10 dark:bg-black/20 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-full shadow-lg">
        <div className={`flex items-center transition-all duration-300 ${
          isMinimized ? 'px-3 py-2' : 'px-4 py-3'
        }`}>
          
          {/* Music Icon with Pulse */}
          <div className="relative">
            <div className={`w-8 h-8 bg-accent-green/20 rounded-full flex items-center justify-center ${
              isPlaying ? 'music-reactive-scale' : ''
            }`}>
              <Music className={`w-4 h-4 text-accent-green ${
                isPlaying ? 'music-reactive-rotate' : ''
              }`} />
            </div>
            {isPlaying && (
              <div className="absolute -inset-1 bg-accent-green/30 rounded-full animate-ping"></div>
            )}
          </div>

          {/* Track Info and Visualizer */}
          {!isMinimized && (
            <>
              <div className="ml-3 flex-1 min-w-0">
                <div className="flex items-center space-x-3">
                  {/* Track Name */}
                  <div className="min-w-0 flex-1">
                    <p className={`text-sm font-medium text-deep-charcoal dark:text-dark-text truncate ${
                      isPlaying ? 'music-reactive-text-glow' : ''
                    }`}>
                      {currentTrack || 'No track playing'}
                    </p>
                    <p className="text-xs text-deep-charcoal/60 dark:text-dark-text/60">
                      {isPlaying ? '🎵 Now Playing' : '⏸️ Paused'}
                    </p>
                  </div>

                  {/* Mini Visualizer */}
                  <div className="flex items-end space-x-0.5 h-6">
                    {getIntensityBars()}
                  </div>
                </div>
              </div>

              {/* Volume Control */}
              <div className="ml-4 flex items-center space-x-2">
                <Volume2 className="w-4 h-4 text-deep-charcoal/60 dark:text-dark-text/60" />
                <div className="w-16 h-1 bg-deep-charcoal/20 dark:bg-dark-text/20 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-accent-green rounded-full transition-all duration-200"
                    style={{ width: `${volume * 100}%` }}
                  />
                </div>
              </div>
            </>
          )}

          {/* Minimize/Maximize Button */}
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className="ml-3 w-6 h-6 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center transition-colors"
            title={isMinimized ? 'Expand' : 'Minimize'}
          >
            {isMinimized ? (
              <Maximize2 className="w-3 h-3 text-deep-charcoal dark:text-dark-text" />
            ) : (
              <Minimize2 className="w-3 h-3 text-deep-charcoal dark:text-dark-text" />
            )}
          </button>

          {/* Close Button */}
          <button
            onClick={() => setIsVisible(false)}
            className="ml-2 w-6 h-6 bg-red-500/20 hover:bg-red-500/30 rounded-full flex items-center justify-center transition-colors"
            title="Hide"
          >
            <span className="text-xs text-red-500">×</span>
          </button>
        </div>

        {/* Progress Bar */}
        {!isMinimized && isPlaying && (
          <div className="px-4 pb-2">
            <div className="w-full h-0.5 bg-deep-charcoal/20 dark:bg-dark-text/20 rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-accent-green to-green-400 rounded-full music-reactive-glow"
                style={{ 
                  width: '45%', // This would be dynamic based on actual progress
                  animation: isPlaying ? 'musicReactiveGlow 2s ease-in-out infinite' : 'none'
                }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
