"use client";

import { useEffect, useState } from 'react';
import { useMusicContext } from './MusicContext';

const ScrollProgress = () => {
  const [scrollProgress, setScrollProgress] = useState(0);
  const { isPlaying } = useMusicContext();

  useEffect(() => {
    const handleScroll = () => {
      const totalHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = (window.scrollY / totalHeight) * 100;
      setScrollProgress(Math.min(progress, 100));
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="fixed top-0 left-0 w-full h-1 bg-gray-200 dark:bg-gray-800 z-50">
      <div 
        className={`h-full transition-all duration-300 ease-out ${
          isPlaying 
            ? 'bg-gradient-to-r from-green-400 via-blue-500 to-purple-600' 
            : 'bg-gradient-to-r from-accent-green to-blue-500'
        }`}
        style={{ width: `${scrollProgress}%` }}
      />
    </div>
  );
};

export default ScrollProgress;
