"use client";

import { useEffect, useRef, useState } from 'react';
import { useMusicContext } from './MusicContext';
import { useTheme } from 'next-themes';

const MusicReactiveCursor = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { isPlaying, musicIntensity } = useMusicContext();
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const mousePos = useRef({ x: 0, y: 0 });
  const trail = useRef<Array<{ x: number; y: number; life: number; size: number; hue: number }>>([]);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Mouse move handler
    const handleMouseMove = (e: MouseEvent) => {
      mousePos.current = { x: e.clientX, y: e.clientY };
      
      if (isPlaying) {
        // Add trail particles when music is playing
        const intensity = musicIntensity || 1;
        const particleCount = Math.floor(intensity * 3) + 1;
        
        for (let i = 0; i < particleCount; i++) {
          trail.current.push({
            x: e.clientX + (Math.random() - 0.5) * 20,
            y: e.clientY + (Math.random() - 0.5) * 20,
            life: 1,
            size: Math.random() * intensity * 5 + 2,
            hue: (Date.now() * 0.1 + Math.random() * 60) % 360
          });
        }
        
        // Limit trail length
        if (trail.current.length > 100) {
          trail.current = trail.current.slice(-100);
        }
      }
    };

    window.addEventListener('mousemove', handleMouseMove);

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Update and draw trail particles
      trail.current = trail.current.filter(particle => {
        particle.life -= 0.02;
        particle.y -= 1; // Float upward
        
        if (particle.life <= 0) return false;

        // Draw particle
        ctx.save();
        ctx.globalAlpha = particle.life;
        
        // Create gradient for each particle
        const gradient = ctx.createRadialGradient(
          particle.x, particle.y, 0,
          particle.x, particle.y, particle.size
        );
        
        const color = `hsl(${particle.hue}, 70%, 60%)`;
        gradient.addColorStop(0, color);
        gradient.addColorStop(1, 'transparent');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();
        
        // Add glow effect
        if (isPlaying) {
          ctx.shadowBlur = particle.size * 2;
          ctx.shadowColor = color;
          ctx.fill();
        }
        
        ctx.restore();
        return true;
      });

      // Draw main cursor effect when music is playing
      if (isPlaying && mousePos.current.x > 0) {
        const intensity = musicIntensity || 1;
        const time = Date.now() * 0.01;
        
        ctx.save();
        
        // Main cursor glow
        const mainGradient = ctx.createRadialGradient(
          mousePos.current.x, mousePos.current.y, 0,
          mousePos.current.x, mousePos.current.y, 30 * intensity
        );
        
        const hue = (time * 50) % 360;
        mainGradient.addColorStop(0, `hsla(${hue}, 70%, 60%, 0.8)`);
        mainGradient.addColorStop(0.5, `hsla(${hue + 60}, 70%, 60%, 0.4)`);
        mainGradient.addColorStop(1, 'transparent');
        
        ctx.fillStyle = mainGradient;
        ctx.beginPath();
        ctx.arc(mousePos.current.x, mousePos.current.y, 30 * intensity, 0, Math.PI * 2);
        ctx.fill();
        
        // Pulsing ring
        ctx.strokeStyle = `hsla(${hue}, 70%, 60%, ${0.5 + Math.sin(time * 2) * 0.3})`;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(
          mousePos.current.x, 
          mousePos.current.y, 
          20 + Math.sin(time * 3) * 10, 
          0, 
          Math.PI * 2
        );
        ctx.stroke();
        
        ctx.restore();
      }

      requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [isPlaying, musicIntensity, resolvedTheme, mounted]);

  if (!mounted) return null;

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-50"
      style={{ 
        background: 'transparent',
        mixBlendMode: resolvedTheme === 'dark' ? 'screen' : 'multiply'
      }}
    />
  );
};

export default MusicReactiveCursor;
