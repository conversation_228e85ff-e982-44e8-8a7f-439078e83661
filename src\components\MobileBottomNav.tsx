"use client";

import { useState, useRef } from 'react';
import { Home, User, Briefcase, Code, Play, Music, Volume2, VolumeX, ExternalLink } from 'lucide-react';
import { useMusicContext } from './MusicContext';

interface MobileBottomNavProps {
  scrollToSection: (sectionId: string) => void;
}

export default function MobileBottomNav({ scrollToSection }: MobileBottomNavProps) {
  // Music Context
  const { isPlaying, setIsPlaying, setCurrentTrack, setMusicIntensity } = useMusicContext();

  // YouTube Player State
  const [isYouTubePlaying, setIsYouTubePlaying] = useState(false);
  const [isYouTubeMuted, setIsYouTubeMuted] = useState(false);
  const youtubePlayerRef = useRef<HTMLIFrameElement>(null);

  // Navigation State
  const [activeSection, setActiveSection] = useState('hero');

  const navItems = [
    { id: 'hero', icon: Home },
    { id: 'about', icon: User },
    { id: 'hire', icon: ExternalLink },
    { id: 'experience', icon: Briefcase },
    { id: 'contributions', icon: Code }, // Contact/Hire Me section
  ];

  const handleNavClick = (sectionId: string) => {
    if (sectionId === 'contactus' || sectionId === 'hire') {
      // Redirect to Cal.com scheduling page
      window.open('https://cal.com/arkit-karmokar-x0uyir/secret?overlayCalendar=true', '_blank');
    } else {
      scrollToSection(sectionId);
      setActiveSection(sectionId);
    }
  };

  const handleYouTubePlay = () => {
    if (youtubePlayerRef.current) {
      const iframe = youtubePlayerRef.current;
      if (isYouTubePlaying) {
        iframe.src = `https://www.youtube.com/embed/jfKfPfyJRdk?enablejsapi=1&controls=0&modestbranding=1&rel=0&showinfo=0`;
        setIsYouTubePlaying(false);
        setIsPlaying(false);
      } else {
        iframe.src = `https://www.youtube.com/embed/jfKfPfyJRdk?enablejsapi=1&autoplay=1&controls=0&modestbranding=1&rel=0&showinfo=0&mute=${isYouTubeMuted ? 1 : 0}`;
        setIsYouTubePlaying(true);
        setIsPlaying(true);
        setCurrentTrack('Mobile Coding Vibes');
        // Simulate music intensity for mobile
        const intensityInterval = setInterval(() => {
          setMusicIntensity(0.5 + Math.random() * 1.5);
        }, 200);

        // Clear interval when music stops
        setTimeout(() => {
          if (!isYouTubePlaying) clearInterval(intensityInterval);
        }, 1000);
      }
    }
  };

  const handleYouTubeMute = () => {
    setIsYouTubeMuted(!isYouTubeMuted);
    if (youtubePlayerRef.current && isYouTubePlaying) {
      const iframe = youtubePlayerRef.current;
      iframe.src = `https://www.youtube.com/embed/jfKfPfyJRdk?enablejsapi=1&autoplay=1&controls=0&modestbranding=1&rel=0&showinfo=0&mute=${!isYouTubeMuted ? 1 : 0}`;
    }
  };

  const handleSpotifyClick = () => {
    window.open('https://open.spotify.com/playlist/37i9dQZF1DXcBWIGoYBM5M', '_blank');
  };

  return (
    <div className="fixed bottom-6 left-4 right-4 z-50 md:hidden">
      {/* Main Navigation Bar */}
      <div className="bg-white/90 dark:bg-black/20 backdrop-blur-xl rounded-full border border-gray-200 dark:border-white/10 shadow-2xl px-3 py-2 relative max-w-sm mx-auto">
        <div className="flex items-center justify-between space-x-2">

          {/* YouTube Music Player */}
          <div className="flex items-center space-x-1">
            <button
              onClick={handleYouTubePlay}
              className={`
                mobile-music-button w-10 h-10 rounded-full flex items-center justify-center transition-all duration-500 relative flex-shrink-0
                ${isYouTubePlaying
                  ? 'bg-red-500 text-white pulse-glow music-spin'
                  : 'bg-gray-700/80 dark:bg-white/20 text-white hover:bg-gray-600/80 dark:hover:bg-white/30'
                }
              `}
              style={{ touchAction: 'manipulation' }}
            >
              {isYouTubePlaying ? (
                <div className="relative">
                  <Music className="w-4 h-4" />
                  <div className="absolute -inset-1 rounded-full border-2 border-white/40 rotating-border"></div>
                </div>
              ) : (
                <Play className="w-4 h-4 ml-0.5" />
              )}
            </button>

            {isYouTubePlaying && (
              <button
                onClick={handleYouTubeMute}
                className="w-7 h-7 bg-gray-700/80 dark:bg-white/20 rounded-full flex items-center justify-center text-white hover:bg-gray-600/80 dark:hover:bg-white/30 transition-all duration-300 flex-shrink-0"
                style={{ touchAction: 'manipulation' }}
              >
                {isYouTubeMuted ? <VolumeX className="w-3 h-3" /> : <Volume2 className="w-3 h-3" />}
              </button>
            )}
          </div>

          {/* Navigation Icons */}
          <div className="flex items-center space-x-1 flex-1 justify-center">
            {navItems.map((item) => {
              const IconComponent = item.icon;
              const isActive = activeSection === item.id;
              return (
                <button
                  key={item.id}
                  onClick={() => handleNavClick(item.id)}
                  className={`
                    w-9 h-9 rounded-full flex items-center justify-center transition-all duration-300 relative flex-shrink-0
                    ${isActive
                      ? 'bg-blue-500 text-white shadow-lg shadow-blue-500/40'
                      : 'text-gray-700 dark:text-white/70 hover:text-gray-900 dark:hover:text-white hover:bg-gray-200 dark:hover:bg-white/20'
                    }
                  `}
                >
                  <IconComponent className="w-4 h-4" />
                  {isActive && (
                    <div className="absolute -inset-0.5 rounded-full border border-blue-300/50 animate-ping"></div>
                  )}
                </button>
              );
            })}
          </div>

          {/* Spotify Music Player */}
          <div className="flex items-center">
            <button
              onClick={handleSpotifyClick}
              className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white hover:bg-green-600 transition-all duration-300 shadow-lg shadow-green-500/40 pulse-glow flex-shrink-0"
            >
              <Music className="w-4 h-4" />
            </button>
          </div>

        </div>

        {/* Animated Sound Waves for YouTube */}
        {isYouTubePlaying && (
          <div className="absolute -bottom-2 left-6 flex space-x-0.5">
            <div className="w-0.5 h-2 bg-red-400/80 rounded-full music-wave" style={{ animationDelay: '0s' }}></div>
            <div className="w-0.5 h-3 bg-red-400/80 rounded-full music-wave" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-0.5 h-2 bg-red-400/80 rounded-full music-wave" style={{ animationDelay: '0.2s' }}></div>
          </div>
        )}

        {/* Spotify indicator */}
        <div className="absolute -bottom-2 right-6 flex space-x-0.5">
          <div className="w-0.5 h-2 bg-green-400/60 rounded-full animate-pulse" style={{ animationDelay: '0s' }}></div>
          <div className="w-0.5 h-2 bg-green-400/60 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          <div className="w-0.5 h-2 bg-green-400/60 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
        </div>
      </div>

      {/* Hidden YouTube Player */}
      <iframe
        ref={youtubePlayerRef}
        width="0"
        height="0"
        style={{ display: 'none' }}
        allow="autoplay; encrypted-media"
      />
    </div>
  );
}
