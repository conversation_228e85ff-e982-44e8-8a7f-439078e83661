"use client";

import { useRef, useState, useEffect } from 'react';
import { useTheme } from 'next-themes';
import * as THREE from 'three';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';

// Audio analyzer component that creates the visualization
const AudioAnalyzer = ({ audioRef, isPlaying }: { audioRef: React.RefObject<HTMLAudioElement>, isPlaying: boolean }) => {
  const { scene } = useThree();
  const analyzerRef = useRef<AnalyserNode | null>(null);
  const dataArrayRef = useRef<Uint8Array | null>(null);
  const particlesRef = useRef<THREE.Points | null>(null);
  const { resolvedTheme } = useTheme();
  
  // Set up audio analyzer
  useEffect(() => {
    if (!audioRef.current) return;
    
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const audioSource = audioContext.createMediaElementSource(audioRef.current);
    const analyzer = audioContext.createAnalyser();
    
    analyzer.fftSize = 256;
    audioSource.connect(analyzer);
    analyzer.connect(audioContext.destination);
    
    const bufferLength = analyzer.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    
    analyzerRef.current = analyzer;
    dataArrayRef.current = dataArray;
    
    // Create particles
    const particleGeometry = new THREE.BufferGeometry();
    const particleCount = bufferLength;
    const particlePositions = new Float32Array(particleCount * 3);
    
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      particlePositions[i3] = (Math.random() - 0.5) * 10;
      particlePositions[i3 + 1] = (Math.random() - 0.5) * 10;
      particlePositions[i3 + 2] = (Math.random() - 0.5) * 10;
    }
    
    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));
    
    const particleMaterial = new THREE.PointsMaterial({
      size: 0.1,
      color: resolvedTheme === 'dark' ? 0x00ff00 : 0x242424,
      transparent: true,
      blending: THREE.AdditiveBlending,
    });
    
    const particles = new THREE.Points(particleGeometry, particleMaterial);
    scene.add(particles);
    particlesRef.current = particles;
    
    return () => {
      if (particlesRef.current) {
        scene.remove(particlesRef.current);
      }
      audioSource.disconnect();
      analyzer.disconnect();
      audioContext.close();
    };
  }, [audioRef, scene, resolvedTheme]);
  
  // Update visualization on each frame
  useFrame(() => {
    if (!analyzerRef.current || !dataArrayRef.current || !particlesRef.current || !isPlaying) return;
    
    analyzerRef.current.getByteFrequencyData(dataArrayRef.current);
    
    const positions = particlesRef.current.geometry.attributes.position.array as Float32Array;
    const bufferLength = dataArrayRef.current.length;
    
    for (let i = 0; i < bufferLength; i++) {
      const value = dataArrayRef.current[i] / 255;
      const i3 = i * 3;
      
      // Update particle positions based on audio data
      const x = positions[i3];
      const y = positions[i3 + 1];
      const z = positions[i3 + 2];
      
      const distance = Math.sqrt(x * x + y * y + z * z);
      const normalizedX = x / distance;
      const normalizedY = y / distance;
      const normalizedZ = z / distance;
      
      positions[i3] = normalizedX * (2 + value * 3);
      positions[i3 + 1] = normalizedY * (2 + value * 3);
      positions[i3 + 2] = normalizedZ * (2 + value * 3);
    }
    
    particlesRef.current.geometry.attributes.position.needsUpdate = true;
    
    // Change color based on average frequency
    const avg = dataArrayRef.current.reduce((sum, value) => sum + value, 0) / bufferLength;
    const hue = (avg / 255) * 360;
    
    (particlesRef.current.material as THREE.PointsMaterial).color.setHSL(hue / 360, 1, 0.5);
    (particlesRef.current.material as THREE.PointsMaterial).size = 0.05 + (avg / 255) * 0.2;
  });
  
  return null;
};

// Main visualizer component
const MusicVisualizer = ({ audioRef, isPlaying = false }: { audioRef: React.RefObject<HTMLAudioElement>, isPlaying: boolean }) => {
  return (
    <div className="fixed inset-0 pointer-events-none z-0 opacity-40">
      <Canvas camera={{ position: [0, 0, 5], fov: 75 }}>
        <ambientLight intensity={0.5} />
        <AudioAnalyzer audioRef={audioRef} isPlaying={isPlaying} />
        <OrbitControls enableZoom={false} enablePan={false} enableRotate={false} />
      </Canvas>
    </div>
  );
};

export default MusicVisualizer;
