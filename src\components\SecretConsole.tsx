"use client";

import { useEffect, useState } from 'react';

const SecretConsole = () => {
  const [showConsole, setShowConsole] = useState(false);
  const [consoleMessages, setConsoleMessages] = useState<string[]>([]);

  useEffect(() => {
    // Detect when developer tools are opened
    let devtools = { open: false, orientation: null };
    const threshold = 160;

    const detectDevTools = () => {
      if (window.outerHeight - window.innerHeight > threshold || 
          window.outerWidth - window.innerWidth > threshold) {
        if (!devtools.open) {
          devtools.open = true;
          setShowConsole(true);
          addConsoleMessage("🕵️ Well, well, well... A fellow developer!");
          addConsoleMessage("🎉 Welcome to the secret console!");
          addConsoleMessage("💡 Try typing some commands...");
          addConsoleMessage("📝 Available commands: help, hack, matrix, party, hire");
        }
      } else {
        devtools.open = false;
        setShowConsole(false);
      }
    };

    // Check every 500ms
    const interval = setInterval(detectDevTools, 500);

    // Also detect F12 key
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'F12') {
        setTimeout(() => {
          setShowConsole(true);
          addConsoleMessage("🔧 F12 detected! Developer mode activated!");
        }, 100);
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    // Add some fun console messages
    console.log("%c🚀 ARKIT'S PORTFOLIO", "color: #4ADE80; font-size: 24px; font-weight: bold;");
    console.log("%cHey there, fellow developer! 👋", "color: #3B82F6; font-size: 16px;");
    console.log("%cI see you're checking out my code. I like your style! 😎", "color: #8B5CF6;");
    console.log("%cWant to work together? Let's chat! 💬", "color: #EF4444;");
    console.log("%cEmail: <EMAIL>", "color: #10B981; font-weight: bold;");

    return () => {
      clearInterval(interval);
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  const addConsoleMessage = (message: string) => {
    setConsoleMessages(prev => [...prev, message]);
  };

  const handleCommand = (command: string) => {
    const cmd = command.toLowerCase().trim();
    
    switch (cmd) {
      case 'help':
        addConsoleMessage("📚 Available commands:");
        addConsoleMessage("  • help - Show this help");
        addConsoleMessage("  • hack - Activate hacker mode");
        addConsoleMessage("  • matrix - Enter the matrix");
        addConsoleMessage("  • party - Party time!");
        addConsoleMessage("  • hire - Hire me info");
        addConsoleMessage("  • clear - Clear console");
        break;
      
      case 'hack':
        addConsoleMessage("🔥 HACKER MODE ACTIVATED!");
        addConsoleMessage("💻 Accessing mainframe...");
        addConsoleMessage("🔐 Bypassing security...");
        addConsoleMessage("✅ Access granted! You're in!");
        document.body.style.animation = 'musicReactiveColorShift 2s ease-in-out';
        setTimeout(() => {
          document.body.style.animation = '';
        }, 2000);
        break;
      
      case 'matrix':
        addConsoleMessage("🔢 Welcome to the Matrix...");
        addConsoleMessage("💊 Red pill or blue pill?");
        addConsoleMessage("🕶️ There is no spoon.");
        // Trigger matrix rain effect
        window.dispatchEvent(new CustomEvent('matrix-activate'));
        break;
      
      case 'party':
        addConsoleMessage("🎉 PARTY TIME! 🎉");
        addConsoleMessage("🕺 Let's dance!");
        addConsoleMessage("🎵 Turn up the music!");
        // Trigger party effects
        for (let i = 0; i < 10; i++) {
          setTimeout(() => {
            const x = Math.random() * window.innerWidth;
            const y = Math.random() * window.innerHeight;
            window.dispatchEvent(new CustomEvent('click', { 
              detail: { clientX: x, clientY: y } 
            }));
          }, i * 200);
        }
        break;
      
      case 'hire':
        addConsoleMessage("💼 HIRE ARKIT KARMOKAR");
        addConsoleMessage("📧 Email: <EMAIL>");
        addConsoleMessage("🔗 LinkedIn: Connect with me!");
        addConsoleMessage("💰 Rate: Let's discuss!");
        addConsoleMessage("⚡ Availability: Ready to start!");
        break;
      
      case 'clear':
        setConsoleMessages([]);
        break;
      
      default:
        addConsoleMessage(`❌ Unknown command: ${command}`);
        addConsoleMessage("💡 Type 'help' for available commands");
    }
  };

  if (!showConsole) return null;

  return (
    <div className="fixed bottom-4 left-4 z-[9999] w-96 h-64 bg-black/95 border border-green-400 rounded-lg font-mono text-sm overflow-hidden">
      <div className="bg-green-400 text-black px-3 py-1 font-bold flex justify-between items-center">
        <span>🔧 Secret Developer Console</span>
        <button 
          onClick={() => setShowConsole(false)}
          className="hover:bg-black/20 px-2 py-1 rounded"
        >
          ✕
        </button>
      </div>
      
      <div className="p-3 h-48 overflow-y-auto text-green-400 text-xs">
        {consoleMessages.map((message, index) => (
          <div key={index} className="mb-1">
            <span className="text-gray-500">{'>'}</span> {message}
          </div>
        ))}
        
        <div className="flex items-center mt-2">
          <span className="text-gray-500 mr-2">{'>'}</span>
          <input
            type="text"
            className="bg-transparent outline-none flex-1 text-green-400"
            placeholder="Type a command..."
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                const command = e.currentTarget.value;
                if (command.trim()) {
                  addConsoleMessage(`> ${command}`);
                  handleCommand(command);
                  e.currentTarget.value = '';
                }
              }
            }}
            autoFocus
          />
          <span className="animate-pulse">_</span>
        </div>
      </div>
    </div>
  );
};

export default SecretConsole;
