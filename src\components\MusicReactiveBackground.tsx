"use client";

import { useRef, useEffect, useState } from 'react';
import { useTheme } from 'next-themes';
import { useMusicContext } from './MusicContext';

const MusicReactiveBackground = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const particlesRef = useRef<any[]>([]);
  const { resolvedTheme } = useTheme();
  const { isPlaying, musicIntensity } = useMusicContext();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    const createParticle = () => ({
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      vx: (Math.random() - 0.5) * 0.5,
      vy: (Math.random() - 0.5) * 0.5,
      size: Math.random() * 2 + 1,
      opacity: Math.random() * 0.5 + 0.1,
      hue: Math.random() * 360,
      life: 1,
    });

    const initParticles = () => {
      particlesRef.current = [];
      const particleCount = isPlaying ? 50 : 20;
      for (let i = 0; i < particleCount; i++) {
        particlesRef.current.push(createParticle());
      }
    };

    const updateParticles = () => {
      const intensity = musicIntensity || 1;
      
      particlesRef.current.forEach((particle, index) => {
        // Update position
        particle.x += particle.vx * (isPlaying ? intensity : 0.5);
        particle.y += particle.vy * (isPlaying ? intensity : 0.5);

        // Wrap around screen
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;

        // Update properties based on music
        if (isPlaying) {
          particle.size = Math.max(0.5, particle.size + (Math.random() - 0.5) * intensity * 0.1);
          particle.opacity = Math.min(0.8, particle.opacity + (Math.random() - 0.5) * intensity * 0.05);
          particle.hue = (particle.hue + intensity * 0.5) % 360;
        }

        // Remove old particles
        particle.life -= 0.001;
        if (particle.life <= 0) {
          particlesRef.current[index] = createParticle();
        }
      });
    };

    const drawParticles = () => {
      particlesRef.current.forEach(particle => {
        ctx.save();
        ctx.globalAlpha = particle.opacity;
        
        // Create gradient for particle
        const gradient = ctx.createRadialGradient(
          particle.x, particle.y, 0,
          particle.x, particle.y, particle.size * 3
        );
        
        if (isPlaying) {
          gradient.addColorStop(0, `hsla(${particle.hue}, 70%, 60%, 0.8)`);
          gradient.addColorStop(1, `hsla(${particle.hue}, 70%, 60%, 0)`);
        } else {
          const baseColor = resolvedTheme === 'dark' ? '220, 220, 220' : '100, 100, 100';
          gradient.addColorStop(0, `rgba(${baseColor}, 0.3)`);
          gradient.addColorStop(1, `rgba(${baseColor}, 0)`);
        }
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
      });
    };

    const drawConnections = () => {
      if (!isPlaying) return;
      
      const maxDistance = 100;
      const intensity = musicIntensity || 1;
      
      for (let i = 0; i < particlesRef.current.length; i++) {
        for (let j = i + 1; j < particlesRef.current.length; j++) {
          const p1 = particlesRef.current[i];
          const p2 = particlesRef.current[j];
          
          const dx = p1.x - p2.x;
          const dy = p1.y - p2.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < maxDistance) {
            const opacity = (1 - distance / maxDistance) * 0.2 * intensity;
            ctx.save();
            ctx.globalAlpha = opacity;
            ctx.strokeStyle = `hsl(${(p1.hue + p2.hue) / 2}, 70%, 60%)`;
            ctx.lineWidth = 0.5;
            ctx.beginPath();
            ctx.moveTo(p1.x, p1.y);
            ctx.lineTo(p2.x, p2.y);
            ctx.stroke();
            ctx.restore();
          }
        }
      }
    };

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Background gradient that changes with music
      if (isPlaying) {
        const gradient = ctx.createRadialGradient(
          canvas.width / 2, canvas.height / 2, 0,
          canvas.width / 2, canvas.height / 2, Math.max(canvas.width, canvas.height)
        );
        const time = Date.now() * 0.001;
        const hue1 = (time * 20) % 360;
        const hue2 = (time * 30 + 180) % 360;
        
        if (resolvedTheme === 'dark') {
          gradient.addColorStop(0, `hsla(${hue1}, 30%, 5%, 0.1)`);
          gradient.addColorStop(1, `hsla(${hue2}, 20%, 3%, 0.05)`);
        } else {
          gradient.addColorStop(0, `hsla(${hue1}, 20%, 95%, 0.1)`);
          gradient.addColorStop(1, `hsla(${hue2}, 15%, 98%, 0.05)`);
        }
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }

      updateParticles();
      drawConnections();
      drawParticles();

      animationRef.current = requestAnimationFrame(animate);
    };

    resizeCanvas();
    initParticles();
    animate();

    window.addEventListener('resize', resizeCanvas);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [mounted, resolvedTheme, isPlaying, musicIntensity]);

  if (!mounted) return null;

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-0"
      style={{ opacity: isPlaying ? 0.6 : 0.3 }}
    />
  );
};

export default MusicReactiveBackground;
