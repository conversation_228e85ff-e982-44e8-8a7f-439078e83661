"use client";

import { useRef, useEffect, useState } from 'react';
import { useTheme } from 'next-themes';
import { useMusicContext } from './MusicContext';

const MusicReactiveBackground = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const particlesRef = useRef<any[]>([]);
  const { resolvedTheme } = useTheme();
  const { isPlaying, musicIntensity } = useMusicContext();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Particle class
    class Particle {
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      baseSize: number;
      color: string;
      alpha: number;
      life: number;
      maxLife: number;

      constructor() {
        this.x = Math.random() * canvas.width;
        this.y = Math.random() * canvas.height;
        this.vx = (Math.random() - 0.5) * 2;
        this.vy = (Math.random() - 0.5) * 2;
        this.baseSize = Math.random() * 3 + 1;
        this.size = this.baseSize;
        this.alpha = Math.random() * 0.5 + 0.1;
        this.life = 0;
        this.maxLife = Math.random() * 200 + 100;
        this.updateColor();
      }

      updateColor() {
        const isDark = resolvedTheme === 'dark';
        const hue = (Date.now() * 0.01 + this.x * 0.01) % 360;
        this.color = isDark 
          ? `hsla(${hue}, 70%, 60%, ${this.alpha})`
          : `hsla(${hue}, 50%, 40%, ${this.alpha})`;
      }

      update() {
        this.x += this.vx;
        this.y += this.vy;
        this.life++;

        // Music reactive effects
        if (isPlaying) {
          const currentIntensity = musicIntensity * (0.5 + Math.sin(Date.now() * 0.01) * 0.5);
          this.size = this.baseSize * (1 + currentIntensity);
          this.vx += (Math.random() - 0.5) * currentIntensity * 0.1;
          this.vy += (Math.random() - 0.5) * currentIntensity * 0.1;
        } else {
          this.size = this.baseSize;
        }

        // Boundary wrapping
        if (this.x < 0) this.x = canvas.width;
        if (this.x > canvas.width) this.x = 0;
        if (this.y < 0) this.y = canvas.height;
        if (this.y > canvas.height) this.y = 0;

        // Life cycle
        if (this.life > this.maxLife) {
          this.life = 0;
          this.x = Math.random() * canvas.width;
          this.y = Math.random() * canvas.height;
        }

        this.updateColor();
      }

      draw(ctx: CanvasRenderingContext2D) {
        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        
        // Add glow effect when music is playing
        if (isPlaying) {
          ctx.shadowBlur = this.size * 2;
          ctx.shadowColor = this.color;
          ctx.fill();
        }
        
        ctx.restore();
      }
    }

    // Initialize particles
    const initParticles = () => {
      particlesRef.current = [];
      const particleCount = isPlaying ? 150 : 80;
      for (let i = 0; i < particleCount; i++) {
        particlesRef.current.push(new Particle());
      }
    };

    initParticles();

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Background gradient that changes with music
      if (isPlaying) {
        const gradient = ctx.createRadialGradient(
          canvas.width / 2, canvas.height / 2, 0,
          canvas.width / 2, canvas.height / 2, Math.max(canvas.width, canvas.height)
        );
        const time = Date.now() * 0.001;
        const hue1 = (time * 20) % 360;
        const hue2 = (time * 30 + 180) % 360;
        
        if (resolvedTheme === 'dark') {
          gradient.addColorStop(0, `hsla(${hue1}, 30%, 5%, 0.1)`);
          gradient.addColorStop(1, `hsla(${hue2}, 20%, 3%, 0.05)`);
        } else {
          gradient.addColorStop(0, `hsla(${hue1}, 20%, 95%, 0.1)`);
          gradient.addColorStop(1, `hsla(${hue2}, 15%, 98%, 0.05)`);
        }
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }

      // Update and draw particles
      particlesRef.current.forEach(particle => {
        particle.update();
        particle.draw(ctx);
      });

      // Draw connections between nearby particles
      if (isPlaying) {
        ctx.strokeStyle = resolvedTheme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
        ctx.lineWidth = 1;
        
        for (let i = 0; i < particlesRef.current.length; i++) {
          for (let j = i + 1; j < particlesRef.current.length; j++) {
            const dx = particlesRef.current[i].x - particlesRef.current[j].x;
            const dy = particlesRef.current[i].y - particlesRef.current[j].y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < 100) {
              ctx.beginPath();
              ctx.moveTo(particlesRef.current[i].x, particlesRef.current[i].y);
              ctx.lineTo(particlesRef.current[j].x, particlesRef.current[j].y);
              ctx.globalAlpha = (100 - distance) / 100 * 0.2;
              ctx.stroke();
              ctx.globalAlpha = 1;
            }
          }
        }
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isPlaying, musicIntensity, resolvedTheme, mounted]);

  if (!mounted) return null;

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-0"
      style={{ 
        background: 'transparent',
        mixBlendMode: resolvedTheme === 'dark' ? 'screen' : 'multiply'
      }}
    />
  );
};

export default MusicReactiveBackground;
