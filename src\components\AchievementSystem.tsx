"use client";

import { useState, useEffect } from 'react';
import { useMusicContext } from './MusicContext';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlocked: boolean;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

const AchievementSystem = () => {
  const [achievements, setAchievements] = useState<Achievement[]>([
    {
      id: 'first_visit',
      title: 'Welcome Visitor!',
      description: 'Discovered this amazing portfolio',
      icon: '👋',
      unlocked: false,
      rarity: 'common'
    },
    {
      id: 'music_lover',
      title: 'Music Lover',
      description: 'Started the music player',
      icon: '🎵',
      unlocked: false,
      rarity: 'common'
    },
    {
      id: 'explorer',
      title: 'Portfolio Explorer',
      description: 'Scrolled through all sections',
      icon: '🗺️',
      unlocked: false,
      rarity: 'rare'
    },
    {
      id: 'clicker',
      title: 'Click Master',
      description: 'Clicked 50 times for explosions',
      icon: '💥',
      unlocked: false,
      rarity: 'rare'
    },
    {
      id: 'konami_master',
      title: 'Ko<PERSON>i Code Master',
      description: 'Unlocked developer mode',
      icon: '🎮',
      unlocked: false,
      rarity: 'epic'
    },
    {
      id: 'typing_champion',
      title: 'Typing Champion',
      description: 'Scored 100+ in typing game',
      icon: '⌨️',
      unlocked: false,
      rarity: 'epic'
    },
    {
      id: 'matrix_hacker',
      title: 'Matrix Hacker',
      description: 'Witnessed the matrix rain',
      icon: '🔢',
      unlocked: false,
      rarity: 'legendary'
    },
    {
      id: 'ultimate_fan',
      title: 'Ultimate Fan',
      description: 'Unlocked all achievements',
      icon: '👑',
      unlocked: false,
      rarity: 'legendary'
    }
  ]);

  const [recentUnlock, setRecentUnlock] = useState<Achievement | null>(null);
  const [clickCount, setClickCount] = useState(0);
  const [hasScrolledAll, setHasScrolledAll] = useState(false);
  const { isPlaying } = useMusicContext();

  // Track achievements
  useEffect(() => {
    // First visit achievement
    setTimeout(() => {
      unlockAchievement('first_visit');
    }, 2000);

    // Scroll tracking
    const handleScroll = () => {
      const scrollPercent = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
      if (scrollPercent > 90 && !hasScrolledAll) {
        setHasScrolledAll(true);
        unlockAchievement('explorer');
      }
    };

    // Click tracking
    const handleClick = () => {
      setClickCount(prev => {
        const newCount = prev + 1;
        if (newCount >= 50) {
          unlockAchievement('clicker');
        }
        return newCount;
      });
    };

    window.addEventListener('scroll', handleScroll);
    window.addEventListener('click', handleClick);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('click', handleClick);
    };
  }, [hasScrolledAll]);

  // Music achievement
  useEffect(() => {
    if (isPlaying) {
      unlockAchievement('music_lover');
      // Matrix hacker achievement for music + time
      setTimeout(() => {
        unlockAchievement('matrix_hacker');
      }, 10000);
    }
  }, [isPlaying]);

  // Global achievement listeners
  useEffect(() => {
    const handleKonamiUnlock = () => unlockAchievement('konami_master');
    const handleTypingChampion = () => unlockAchievement('typing_champion');

    window.addEventListener('konami-unlocked', handleKonamiUnlock);
    window.addEventListener('typing-champion', handleTypingChampion);

    return () => {
      window.removeEventListener('konami-unlocked', handleKonamiUnlock);
      window.removeEventListener('typing-champion', handleTypingChampion);
    };
  }, []);

  const unlockAchievement = (id: string) => {
    setAchievements(prev => {
      const updated = prev.map(achievement => {
        if (achievement.id === id && !achievement.unlocked) {
          const unlockedAchievement = { ...achievement, unlocked: true };
          setRecentUnlock(unlockedAchievement);
          
          // Hide notification after 4 seconds
          setTimeout(() => setRecentUnlock(null), 4000);
          
          return unlockedAchievement;
        }
        return achievement;
      });

      // Check for ultimate fan achievement
      const unlockedCount = updated.filter(a => a.unlocked && a.id !== 'ultimate_fan').length;
      if (unlockedCount === updated.length - 1) {
        setTimeout(() => unlockAchievement('ultimate_fan'), 1000);
      }

      return updated;
    });
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'from-gray-400 to-gray-600';
      case 'rare': return 'from-blue-400 to-blue-600';
      case 'epic': return 'from-purple-400 to-purple-600';
      case 'legendary': return 'from-yellow-400 to-orange-500';
      default: return 'from-gray-400 to-gray-600';
    }
  };

  const getRarityGlow = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'shadow-gray-500/50';
      case 'rare': return 'shadow-blue-500/50';
      case 'epic': return 'shadow-purple-500/50';
      case 'legendary': return 'shadow-yellow-500/50';
      default: return 'shadow-gray-500/50';
    }
  };

  return (
    <>
      {/* Achievement Notification */}
      {recentUnlock && (
        <div className="fixed top-4 right-4 z-[9999] animate-in slide-in-from-right-2 duration-700">
          <div className={`bg-gradient-to-r ${getRarityColor(recentUnlock.rarity)} text-white p-4 rounded-lg shadow-xl ${getRarityGlow(recentUnlock.rarity)} max-w-sm`}>
            <div className="flex items-center space-x-3">
              <div className="text-3xl">{recentUnlock.icon}</div>
              <div>
                <div className="font-bold text-lg">Achievement Unlocked!</div>
                <div className="font-semibold">{recentUnlock.title}</div>
                <div className="text-sm opacity-90">{recentUnlock.description}</div>
                <div className="text-xs mt-1 opacity-75 uppercase font-bold">
                  {recentUnlock.rarity}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Achievement Counter */}
      <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50">
        <div className="bg-black/70 text-white px-4 py-2 rounded-full text-sm font-mono">
          🏆 {achievements.filter(a => a.unlocked).length}/{achievements.length} Achievements
        </div>
      </div>

      {/* Achievement Panel Toggle */}
      <div className="fixed bottom-4 right-20 z-50">
        <details className="group">
          <summary className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-4 py-2 rounded-full cursor-pointer font-bold shadow-lg hover:shadow-xl transition-all duration-300 list-none">
            🏆 Achievements
          </summary>
          
          <div className="absolute bottom-full right-0 mb-2 bg-gray-900 border border-gray-700 rounded-lg p-4 w-80 max-h-96 overflow-y-auto">
            <h3 className="text-white font-bold mb-3 text-center">🏆 Achievement Gallery 🏆</h3>
            <div className="space-y-2">
              {achievements.map(achievement => (
                <div
                  key={achievement.id}
                  className={`flex items-center space-x-3 p-2 rounded ${
                    achievement.unlocked 
                      ? `bg-gradient-to-r ${getRarityColor(achievement.rarity)} text-white` 
                      : 'bg-gray-800 text-gray-500'
                  }`}
                >
                  <div className="text-2xl">
                    {achievement.unlocked ? achievement.icon : '🔒'}
                  </div>
                  <div className="flex-1">
                    <div className="font-semibold text-sm">{achievement.title}</div>
                    <div className="text-xs opacity-75">{achievement.description}</div>
                    {achievement.unlocked && (
                      <div className="text-xs mt-1 opacity-75 uppercase font-bold">
                        {achievement.rarity}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </details>
      </div>
    </>
  );
};

export default AchievementSystem;
