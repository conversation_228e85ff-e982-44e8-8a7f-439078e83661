{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@react-three/drei": "^10.5.1", "@react-three/fiber": "^9.2.0", "@types/three": "^0.178.1", "framer-motion": "^12.23.6", "gsap": "^3.13.0", "next": "15.4.2", "next-themes": "^0.4.6", "react": "19.1.0", "react-audio-visualize": "^1.2.0", "react-dom": "19.1.0", "three": "^0.178.0", "web-audio-api": "^0.2.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "typescript": "^5"}}