"use client";

import { useState, useEffect } from 'react';
import { Play, Pause, Music } from 'lucide-react';

interface SpotifyPlayerProps {
  playlistId?: string;
  autoShow?: boolean;
}

export default function SpotifyPlayer({ 
  playlistId = "37i9dQZF1DXcBWIGoYBM5M", // Default: Today's Top Hits
  autoShow = true 
}: SpotifyPlayerProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);

  // Show the player after a delay when the page loads
  useEffect(() => {
    if (autoShow) {
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 3000); // Show after 3 seconds

      return () => clearTimeout(timer);
    }
  }, [autoShow]);

  const handlePlayPlaylist = () => {
    setHasInteracted(true);
    
    // Create Spotify Web Player URL
    const spotifyUrl = `https://open.spotify.com/playlist/${playlistId}?utm_source=generator&autoplay=1`;
    
    // Open Spotify in a new tab
    window.open(spotifyUrl, '_blank', 'width=400,height=600');
    setIsPlaying(true);
    
    // Hide the player after opening
    setTimeout(() => {
      setIsVisible(false);
    }, 1000);
  };

  const handleDismiss = () => {
    setIsVisible(false);
    setHasInteracted(true);
  };

  // Don't show if user has already interacted
  if (!isVisible || hasInteracted) {
    return null;
  }

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 animate-in fade-in duration-300" />
      
      {/* Player Modal */}
      <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 animate-in slide-in-from-bottom-4 duration-500">
        <div className="bg-white dark:bg-dark-surface rounded-2xl p-6 shadow-2xl border border-deep-charcoal/10 dark:border-dark-text/10 max-w-sm mx-4">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                <Music className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-deep-charcoal dark:text-dark-text">
                  🎵 My Favorite Playlist
                </h3>
                <p className="text-sm text-deep-charcoal/70 dark:text-dark-text/70">
                  Want to vibe while browsing?
                </p>
              </div>
            </div>
            <button
              onClick={handleDismiss}
              className="text-deep-charcoal/50 dark:text-dark-text/50 hover:text-deep-charcoal dark:hover:text-dark-text transition-colors"
            >
              ✕
            </button>
          </div>

          {/* Description */}
          <p className="text-sm text-deep-charcoal/80 dark:text-dark-text/80 mb-6 leading-relaxed">
            I&apos;ve curated the perfect coding playlist! Click below to open it in Spotify and enjoy some great music while exploring my portfolio.
          </p>

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <button
              onClick={handlePlayPlaylist}
              className="flex-1 bg-green-500 hover:bg-green-600 text-white font-medium px-4 py-3 rounded-lg transition-colors flex items-center justify-center space-x-2"
            >
              <Play className="w-4 h-4" />
              <span>Play on Spotify</span>
            </button>
            <button
              onClick={handleDismiss}
              className="px-4 py-3 text-deep-charcoal/70 dark:text-dark-text/70 hover:text-deep-charcoal dark:hover:text-dark-text transition-colors"
            >
              Maybe later
            </button>
          </div>

          {/* Spotify Attribution */}
          <div className="mt-4 pt-4 border-t border-deep-charcoal/10 dark:border-dark-text/10">
            <p className="text-xs text-deep-charcoal/50 dark:text-dark-text/50 text-center">
              Powered by Spotify • Requires Spotify app or web player
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
