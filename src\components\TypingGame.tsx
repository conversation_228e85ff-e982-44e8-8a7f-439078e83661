"use client";

import { useState, useEffect, useRef } from 'react';
import { useMusicContext } from './MusicContext';

const TypingGame = () => {
  const [isGameActive, setIsGameActive] = useState(false);
  const [currentCode, setCurrentCode] = useState('');
  const [userInput, setUserInput] = useState('');
  const [score, setScore] = useState(0);
  const [wpm, setWpm] = useState(0);
  const [timeLeft, setTimeLeft] = useState(60);
  const [gameStarted, setGameStarted] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const startTimeRef = useRef<number>(0);
  const { setMusicIntensity } = useMusicContext();

  const codeSnippets = [
    "const portfolio = () => { return 'awesome'; }",
    "function hackTheMatrix() { console.log('Neo'); }",
    "if (developer.isAwesome()) { hire(developer); }",
    "const skills = ['React', 'TypeScript', 'Node.js'];",
    "while (coding) { drinkCoffee(); }",
    "async function buildAmazingThings() { await magic(); }",
    "const bug = null; // This will never happen",
    "git commit -m 'Fixed everything forever'",
    "sudo make me a sandwich",
    "import { talent } from './arkit-karmokar';",
    "const isHired = checkPortfolio() && playGame();",
    "export default function Developer() { return <Genius />; }"
  ];

  useEffect(() => {
    if (gameStarted && timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0) {
      endGame();
    }
  }, [timeLeft, gameStarted]);

  useEffect(() => {
    if (gameStarted && userInput.length > 0) {
      const timeElapsed = (Date.now() - startTimeRef.current) / 1000 / 60; // minutes
      const wordsTyped = userInput.split(' ').length;
      setWpm(Math.round(wordsTyped / timeElapsed));
    }
  }, [userInput, gameStarted]);

  const startGame = () => {
    setIsGameActive(true);
    setGameStarted(true);
    setScore(0);
    setUserInput('');
    setTimeLeft(60);
    setWpm(0);
    startTimeRef.current = Date.now();
    generateNewCode();
    inputRef.current?.focus();
    setMusicIntensity(2); // Boost music for gaming!
  };

  const endGame = () => {
    setGameStarted(false);
    setMusicIntensity(1);
    
    // Show final score with celebration
    if (score > 50) {
      document.body.style.animation = 'musicReactiveColorShift 2s ease-in-out';
      setTimeout(() => {
        document.body.style.animation = '';
      }, 2000);
    }
  };

  const generateNewCode = () => {
    const randomCode = codeSnippets[Math.floor(Math.random() * codeSnippets.length)];
    setCurrentCode(randomCode);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setUserInput(value);

    // Check if user completed the current code
    if (value === currentCode) {
      setScore(score + currentCode.length);
      setUserInput('');
      generateNewCode();
      
      // Visual feedback
      setMusicIntensity(3);
      setTimeout(() => setMusicIntensity(2), 200);
    }
  };

  const getAccuracy = () => {
    if (userInput.length === 0) return 100;
    let correct = 0;
    for (let i = 0; i < userInput.length; i++) {
      if (userInput[i] === currentCode[i]) correct++;
    }
    return Math.round((correct / userInput.length) * 100);
  };

  const renderCodeWithHighlight = () => {
    return currentCode.split('').map((char, index) => {
      let className = 'text-gray-400';
      
      if (index < userInput.length) {
        className = userInput[index] === char 
          ? 'text-green-400 bg-green-400/20' 
          : 'text-red-400 bg-red-400/20';
      } else if (index === userInput.length) {
        className = 'text-white bg-blue-400/50 animate-pulse';
      }
      
      return (
        <span key={index} className={className}>
          {char}
        </span>
      );
    });
  };

  if (!isGameActive) {
    return (
      <div className="fixed bottom-20 right-4 z-50">
        <button
          onClick={startGame}
          className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-full font-bold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 animate-pulse"
        >
          🎮 Play Typing Game!
        </button>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-[9998] flex items-center justify-center p-4">
      <div className="bg-gray-900 border-2 border-green-400 rounded-lg p-6 max-w-4xl w-full max-h-[80vh] overflow-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-green-400 font-mono">
            🚀 Code Typing Challenge
          </h2>
          <button
            onClick={() => setIsGameActive(false)}
            className="text-red-400 hover:text-red-300 text-xl font-bold"
          >
            ✕
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 text-center">
          <div className="bg-gray-800 p-3 rounded">
            <div className="text-blue-400 font-bold">Time</div>
            <div className="text-2xl text-white">{timeLeft}s</div>
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <div className="text-green-400 font-bold">Score</div>
            <div className="text-2xl text-white">{score}</div>
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <div className="text-yellow-400 font-bold">WPM</div>
            <div className="text-2xl text-white">{wpm}</div>
          </div>
          <div className="bg-gray-800 p-3 rounded">
            <div className="text-purple-400 font-bold">Accuracy</div>
            <div className="text-2xl text-white">{getAccuracy()}%</div>
          </div>
        </div>

        {/* Code Display */}
        <div className="bg-black p-4 rounded-lg mb-4 font-mono text-lg leading-relaxed">
          <div className="text-gray-500 text-sm mb-2">Type this code:</div>
          <div className="text-white">
            {renderCodeWithHighlight()}
          </div>
        </div>

        {/* Input */}
        <input
          ref={inputRef}
          type="text"
          value={userInput}
          onChange={handleInputChange}
          className="w-full bg-gray-800 text-white p-4 rounded-lg font-mono text-lg border-2 border-gray-600 focus:border-green-400 outline-none"
          placeholder="Start typing the code above..."
          disabled={!gameStarted || timeLeft === 0}
        />

        {/* Game Over */}
        {timeLeft === 0 && (
          <div className="mt-6 text-center">
            <div className="text-3xl font-bold text-yellow-400 mb-2">
              🎉 Game Over! 🎉
            </div>
            <div className="text-xl text-white mb-4">
              Final Score: {score} points | WPM: {wpm} | Accuracy: {getAccuracy()}%
            </div>
            <div className="text-green-400">
              {score > 100 ? "🔥 LEGENDARY CODER! 🔥" : 
               score > 50 ? "💪 Great job!" : 
               "🎯 Keep practicing!"}
            </div>
            <button
              onClick={startGame}
              className="mt-4 bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg font-bold transition-colors"
            >
              Play Again
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default TypingGame;
