@import "tailwindcss";

:root {
  --background: #EEE5DA;
  --foreground: #242424;
  --accent: #4ADE80;
}

.dark {
  --background: #0F0F0F;
  --foreground: #E5E5E5;
  --accent: #4ADE80;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
  height: 100%;
}

body {
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  min-height: 100%;
}

/* Prevent scroll issues */
* {
  box-sizing: border-box;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #22c55e;
}

/* Animation classes for GSAP */
.fade-in {
  opacity: 0;
  transform: translateY(50px);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
}

.parallax-slow {
  will-change: transform;
}

.parallax-fast {
  will-change: transform;
}

/* Mobile menu animations */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-menu-enter {
  animation: slideInFromTop 0.3s ease-out;
}

/* Music Player Animations */
@keyframes musicSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes musicPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes musicWave {
  0%, 100% {
    transform: scaleY(1);
  }
  25% {
    transform: scaleY(1.2);
  }
  50% {
    transform: scaleY(0.8);
  }
  75% {
    transform: scaleY(1.1);
  }
}

.music-spin {
  animation: musicSpin 3s linear infinite;
}

.music-pulse {
  animation: musicPulse 2s ease-in-out infinite;
}

.music-wave {
  animation: musicWave 1.5s ease-in-out infinite;
}

/* Floating music button hover effect */
.music-player-container:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
}

/* Mobile Bottom Navigation Glassmorphism */
.mobile-nav-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .mobile-nav-glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Rotating border animation for active music */
@keyframes rotateBorder {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.rotating-border {
  animation: rotateBorder 3s linear infinite;
}

/* Pulsing glow effect */
@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px currentColor;
  }
  50% {
    box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
  }
}

.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

/* Music-Reactive Classes */
@keyframes musicReactiveFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes musicReactiveRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes musicReactiveScale {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes musicReactiveGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(34, 197, 94, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.6), 0 0 30px rgba(34, 197, 94, 0.4);
  }
}

@keyframes musicReactiveColorShift {
  0% {
    filter: hue-rotate(0deg);
  }
  25% {
    filter: hue-rotate(90deg);
  }
  50% {
    filter: hue-rotate(180deg);
  }
  75% {
    filter: hue-rotate(270deg);
  }
  100% {
    filter: hue-rotate(360deg);
  }
}

@keyframes musicReactiveBorderPulse {
  0%, 100% {
    border-color: rgba(34, 197, 94, 0.3);
    border-width: 1px;
  }
  50% {
    border-color: rgba(34, 197, 94, 0.8);
    border-width: 2px;
  }
}

@keyframes musicReactiveTextGlow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(34, 197, 94, 0.3);
  }
  50% {
    text-shadow: 0 0 15px rgba(34, 197, 94, 0.8), 0 0 25px rgba(34, 197, 94, 0.5);
  }
}

/* Music-Reactive Animation Classes */
.music-reactive-float {
  animation: musicReactiveFloat 2s ease-in-out infinite;
}

.music-reactive-rotate {
  animation: musicReactiveRotate 8s linear infinite;
}

.music-reactive-scale {
  animation: musicReactiveScale 1.5s ease-in-out infinite;
}

.music-reactive-glow {
  animation: musicReactiveGlow 2s ease-in-out infinite;
}

.music-reactive-color {
  animation: musicReactiveColorShift 6s linear infinite;
}

.music-reactive-border {
  animation: musicReactiveBorderPulse 2s ease-in-out infinite;
  border: 1px solid transparent;
}

.music-reactive-text-glow {
  animation: musicReactiveTextGlow 2s ease-in-out infinite;
}

/* Intensity-based classes */
.music-intensity-low {
  animation-duration: 3s;
  opacity: 0.7;
}

.music-intensity-medium {
  animation-duration: 2s;
  opacity: 0.85;
}

.music-intensity-high {
  animation-duration: 1s;
  opacity: 1;
}

/* Combined effects for professional look */
.music-reactive-card {
  transition: all 0.3s ease;
}

.music-reactive-card.music-active {
  animation: musicReactiveScale 2s ease-in-out infinite, musicReactiveGlow 2s ease-in-out infinite;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.music-reactive-nav {
  transition: all 0.3s ease;
}

.music-reactive-nav.music-active {
  animation: musicReactiveGlow 3s ease-in-out infinite;
  backdrop-filter: blur(10px) saturate(1.2);
}

.music-reactive-logo {
  transition: all 0.3s ease;
}

.music-reactive-logo.music-active {
  animation: musicReactiveFloat 3s ease-in-out infinite, musicReactiveGlow 3s ease-in-out infinite;
}

.music-reactive-text {
  transition: all 0.3s ease;
}

.music-reactive-text.music-active {
  animation: musicReactiveTextGlow 2.5s ease-in-out infinite;
}

/* Theme variables moved to :root */

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, sans-serif;
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

* {
  box-sizing: border-box;
}
