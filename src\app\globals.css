@import "tailwindcss";

:root {
  --background: #EEE5DA;
  --foreground: #242424;
  --accent: #4ADE80;
}

.dark {
  --background: #0F0F0F;
  --foreground: #E5E5E5;
  --accent: #4ADE80;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
  height: 100%;
}

body {
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  min-height: 100%;
}

/* Prevent scroll issues */
* {
  box-sizing: border-box;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #22c55e;
}

/* Animation classes for GSAP */
.fade-in {
  opacity: 0;
  transform: translateY(50px);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
}

.parallax-slow {
  will-change: transform;
}

.parallax-fast {
  will-change: transform;
}

/* Mobile menu animations */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-menu-enter {
  animation: slideInFromTop 0.3s ease-out;
}

/* Music Player Animations */
@keyframes musicSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes musicPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes musicWave {
  0%, 100% {
    transform: scaleY(1);
  }
  25% {
    transform: scaleY(1.2);
  }
  50% {
    transform: scaleY(0.8);
  }
  75% {
    transform: scaleY(1.1);
  }
}

.music-spin {
  animation: musicSpin 3s linear infinite;
}

.music-pulse {
  animation: musicPulse 2s ease-in-out infinite;
}

.music-wave {
  animation: musicWave 1.5s ease-in-out infinite;
}

/* Floating music button hover effect */
.music-player-container:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
}

/* Mobile Bottom Navigation Glassmorphism */
.mobile-nav-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .mobile-nav-glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Rotating border animation for active music */
@keyframes rotateBorder {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.rotating-border {
  animation: rotateBorder 3s linear infinite;
}

/* Pulsing glow effect */
@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px currentColor;
  }
  50% {
    box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
  }
}

.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

/* Theme variables moved to :root */

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, sans-serif;
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

* {
  box-sizing: border-box;
}

/* Music Reactive Animations */
@keyframes musicReactiveGlow {
  0%, 100% {
    filter: drop-shadow(0 0 5px rgba(74, 222, 128, 0.3));
  }
  25% {
    filter: drop-shadow(0 0 15px rgba(239, 68, 68, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.6));
  }
  75% {
    filter: drop-shadow(0 0 15px rgba(168, 85, 247, 0.5));
  }
}

@keyframes musicReactiveScale {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes musicReactiveFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes musicReactiveRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes musicReactiveColorShift {
  0% { color: #4ADE80; }
  25% { color: #EF4444; }
  50% { color: #3B82F6; }
  75% { color: #A855F7; }
  100% { color: #4ADE80; }
}

.music-reactive-glow {
  animation: musicReactiveGlow 3s ease-in-out infinite;
}

.music-reactive-scale {
  animation: musicReactiveScale 2s ease-in-out infinite;
}

.music-reactive-float {
  animation: musicReactiveFloat 4s ease-in-out infinite;
}

.music-reactive-rotate {
  animation: musicReactiveRotate 8s linear infinite;
}

.music-reactive-color {
  animation: musicReactiveColorShift 5s ease-in-out infinite;
}

/* Particle effects */
@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-10px) translateX(5px);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-5px) translateX(-5px);
    opacity: 1;
  }
  75% {
    transform: translateY(-15px) translateX(3px);
    opacity: 0.5;
  }
}

.particle-float {
  animation: particleFloat 6s ease-in-out infinite;
}

/* Enhanced music player effects */
.music-active {
  position: relative;
}

.music-active::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #4ADE80, #EF4444, #3B82F6, #A855F7);
  border-radius: inherit;
  z-index: -1;
  animation: musicReactiveRotate 3s linear infinite;
}

.music-active::after {
  content: '';
  position: absolute;
  inset: 0;
  background: inherit;
  border-radius: inherit;
  z-index: -1;
}
