"use client";

import { useEffect, useState, useRef } from 'react';
import { useMusicContext } from './MusicContext';

const KonamiCode = () => {
  const [isDevMode, setIsDevMode] = useState(false);
  const [showSecret, setShowSecret] = useState(false);
  const sequenceRef = useRef<string[]>([]);
  const { setMusicIntensity } = useMusicContext();

  // Konami Code: ↑↑↓↓←→←→BA
  const konamiSequence = [
    'ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown',
    'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight',
    'KeyB', 'KeyA'
  ];

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      sequenceRef.current.push(event.code);
      
      // Keep only the last 10 keys
      if (sequenceRef.current.length > 10) {
        sequenceRef.current = sequenceRef.current.slice(-10);
      }

      // Check if sequence matches Konami Code
      if (sequenceRef.current.length === 10) {
        const matches = konamiSequence.every((key, index) => 
          key === sequenceRef.current[index]
        );

        if (matches) {
          activateDevMode();
          sequenceRef.current = []; // Reset sequence
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  const activateDevMode = () => {
    setIsDevMode(true);
    setShowSecret(true);
    
    // Play success sound effect (if possible)
    try {
      const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
      audio.play().catch(() => {}); // Ignore errors
    } catch (e) {}

    // Crazy visual effects
    document.body.style.animation = 'musicReactiveColorShift 0.5s ease-in-out';
    
    // Super intense music effects
    setMusicIntensity(5);
    
    // Show secret message
    setTimeout(() => {
      setShowSecret(false);
    }, 5000);

    // Reset after 30 seconds
    setTimeout(() => {
      setIsDevMode(false);
      setMusicIntensity(1);
      document.body.style.animation = '';
    }, 30000);
  };

  if (!isDevMode) return null;

  return (
    <>
      {/* Secret Message */}
      {showSecret && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center pointer-events-none">
          <div className="bg-black/90 text-green-400 p-8 rounded-lg border-2 border-green-400 font-mono text-center animate-pulse">
            <div className="text-4xl mb-4">🎮 DEVELOPER MODE ACTIVATED! 🎮</div>
            <div className="text-xl mb-2">Welcome to the Matrix, Neo...</div>
            <div className="text-sm opacity-70">Enjoy 30 seconds of INSANE effects!</div>
          </div>
        </div>
      )}

      {/* Floating Dev Stats */}
      <div className="fixed top-4 left-4 z-50 bg-black/80 text-green-400 p-4 rounded-lg border border-green-400 font-mono text-xs">
        <div className="text-green-300 font-bold mb-2">🔥 DEV MODE ACTIVE 🔥</div>
        <div>FPS: {Math.floor(Math.random() * 60 + 60)}</div>
        <div>Particles: {Math.floor(Math.random() * 1000 + 5000)}</div>
        <div>CPU: {Math.floor(Math.random() * 30 + 70)}%</div>
        <div>Memory: {Math.floor(Math.random() * 500 + 1500)}MB</div>
        <div className="text-red-400 mt-2">⚠️ MAXIMUM OVERDRIVE ⚠️</div>
      </div>

      {/* Crazy Border Effect */}
      <div className="fixed inset-0 pointer-events-none z-40">
        <div className="absolute inset-0 border-4 border-green-400 animate-pulse"></div>
        <div className="absolute inset-2 border-2 border-red-400 music-reactive-rotate"></div>
        <div className="absolute inset-4 border border-blue-400 music-reactive-rotate" style={{ animationDirection: 'reverse' }}></div>
      </div>

      {/* Floating Code Snippets */}
      <div className="fixed inset-0 pointer-events-none z-30 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute text-green-400 font-mono text-xs opacity-70 animate-bounce"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 2}s`,
              animationDuration: `${2 + Math.random() * 3}s`
            }}
          >
            {['console.log("HACKED!")', 'sudo rm -rf /', 'while(true) party()', 'if(awesome) return true', 'hack.the.planet()', 'matrix.enter()', '01001000 01101001'][Math.floor(Math.random() * 7)]}
          </div>
        ))}
      </div>
    </>
  );
};

export default KonamiCode;
