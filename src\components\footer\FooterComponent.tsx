"use client";

import Link from "next/link";

// GitHub Icon Component
const GitHubIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 0C5.374 0 0 5.373 0 12 0 17.302 3.438 21.8 8.207 23.387c.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z"/>
  </svg>
);

export default function Footer() {
  return (
    <footer className="bg-deep-charcoal  mt-12 dark:bg-dark-surface border-t border-deep-charcoal/10 dark:border-dark-text/10">
      <div className="px-6 lg:px-12 py-12 max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-4 gap-8 lg:gap-12">
          {/* Brand Section */}
          <div className="lg:col-span-2 space-y-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-light-almond dark:bg-dark-text rounded-full flex items-center justify-center">
                <div className="w-5 h-5 bg-deep-charcoal dark:bg-dark-bg rounded-full"></div>
              </div>
              <div>
                <h3 className="text-xl font-bold text-light-almond dark:text-dark-text">
                  Arkit_k
                </h3>
                <p className="text-sm text-light-almond/70 dark:text-dark-text/70">
                  Software Engineer & Open Source Maintainer
                </p>
              </div>
            </div>
            <p className="text-light-almond/80 dark:text-dark-text/80 max-w-md leading-relaxed">
              Passionate about building innovative solutions and contributing to the open-source community.
              Let's create something amazing together.
            </p>

            {/* Social Links */}
            <div className="flex items-center space-x-4">
              <a
                href="https://github.com/Arkit-k"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-light-almond/10 dark:bg-dark-text/10 hover:bg-accent-green/20 rounded-lg flex items-center justify-center transition-colors group"
                aria-label="GitHub"
              >
                <GitHubIcon />
              </a>
              <a
                href="https://www.linkedin.com/in/arkit-karmokar-907493246/"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-light-almond/10 dark:bg-dark-text/10 hover:bg-accent-green/20 rounded-lg flex items-center justify-center transition-colors group"
                aria-label="LinkedIn"
              >
                <svg className="w-5 h-5 text-light-almond dark:text-dark-text group-hover:text-accent-green transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
              <a
                href="https://x.com/DamnArkit"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-light-almond/10 dark:bg-dark-text/10 hover:bg-accent-green/20 rounded-lg flex items-center justify-center transition-colors group"
                aria-label="Twitter"
              >
                <svg className="w-5 h-5 text-light-almond dark:text-dark-text group-hover:text-accent-green transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                </svg>
              </a>
              <a
                href="mailto:<EMAIL>"
                className="w-10 h-10 bg-light-almond/10 dark:bg-dark-text/10 hover:bg-accent-green/20 rounded-lg flex items-center justify-center transition-colors group"
                aria-label="Email"
              >
                <svg className="w-5 h-5 text-light-almond dark:text-dark-text group-hover:text-accent-green transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold text-light-almond dark:text-dark-text">
              Quick Links
            </h4>
            <nav className="space-y-3">
              <Link href="#about" className="block text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors">
                About Me
              </Link>
              <Link href="#experience" className="block text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors">
                Experience
              </Link>
              <Link href="#hire" className="block text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors">
                Hire Me
              </Link>
              <Link href="#contributions" className="block text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors">
                Contributions
              </Link>
              <a
                href="https://drive.google.com/file/d/18jaJPxTYUVgLgRrrkkz-Mz8-cW6fAxnr/view"
                target="_blank"
                rel="noopener noreferrer"
                className="block text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors"
              >
                Resume
              </a>
            </nav>
          </div>

          {/* Contact Info */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold text-light-almond dark:text-dark-text">
              Get In Touch
            </h4>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-accent-green/20 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Email</p>
                  <a href="mailto:<EMAIL>" className="text-light-almond dark:text-dark-text hover:text-accent-green transition-colors">
                    <EMAIL>
                  </a>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-accent-green/20 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Location</p>
                  <p className="text-light-almond dark:text-dark-text">
                    India
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-accent-green/20 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-accent-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-light-almond/70 dark:text-dark-text/70">Availability</p>
                  <p className="text-light-almond dark:text-dark-text">
                    Open to opportunities
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-8 border-t border-light-almond/10 dark:border-dark-text/10">
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            <div className="flex items-center space-x-6 text-sm text-light-almond/70 dark:text-dark-text/70">
              <p>&copy; 2025 Arkit_k. All rights reserved.</p>
              <span className="hidden md:block">•</span>
              <p className="hidden md:block">Built with Next.js & Tailwind CSS</p>
            </div>

            <div className="flex items-center space-x-4 text-sm">
              <Link href="/privacy" className="text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors">
                Privacy
              </Link>
              <span className="text-light-almond/30 dark:text-dark-text/30">•</span>
              <Link href="/terms" className="text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors">
                Terms
              </Link>
              <span className="text-light-almond/30 dark:text-dark-text/30">•</span>
              <button
                onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                className="text-light-almond/70 dark:text-dark-text/70 hover:text-accent-green transition-colors flex items-center space-x-1"
              >
                <span>Back to top</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
