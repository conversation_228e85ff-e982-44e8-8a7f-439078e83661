"use client";

import { useState } from 'react';
import { Mail, MessageCircle, Phone, X } from 'lucide-react';

const FloatingContactButton = () => {
  const [isOpen, setIsOpen] = useState(false);

  const contactOptions = [
    {
      icon: Mail,
      label: 'Email',
      href: 'mailto:<EMAIL>',
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      icon: MessageCircle,
      label: 'LinkedIn',
      href: 'https://linkedin.com/in/arkit-karmokar',
      color: 'bg-blue-700 hover:bg-blue-800'
    },
    {
      icon: Phone,
      label: 'Schedule Call',
      href: 'https://cal.com/arkit-karmokar-x0uyir/secret',
      color: 'bg-green-500 hover:bg-green-600'
    }
  ];

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Contact Options */}
      {isOpen && (
        <div className="absolute bottom-16 right-0 space-y-3 animate-in slide-in-from-bottom-2 duration-300">
          {contactOptions.map((option, index) => (
            <a
              key={option.label}
              href={option.href}
              target="_blank"
              rel="noopener noreferrer"
              className={`
                flex items-center space-x-3 px-4 py-3 rounded-full text-white font-medium
                shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200
                ${option.color}
              `}
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <option.icon className="w-5 h-5" />
              <span className="whitespace-nowrap">{option.label}</span>
            </a>
          ))}
        </div>
      )}

      {/* Main Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          w-14 h-14 rounded-full shadow-lg hover:shadow-xl
          transform hover:scale-110 transition-all duration-300
          flex items-center justify-center text-white font-bold
          ${isOpen 
            ? 'bg-red-500 hover:bg-red-600 rotate-45' 
            : 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'
          }
        `}
      >
        {isOpen ? <X className="w-6 h-6" /> : <Mail className="w-6 h-6" />}
      </button>
    </div>
  );
};

export default FloatingContactButton;
