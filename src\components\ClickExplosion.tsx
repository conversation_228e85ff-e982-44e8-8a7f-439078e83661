"use client";

import { useEffect, useRef, useState } from 'react';
import { useMusicContext } from './MusicContext';
import { useTheme } from 'next-themes';

interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  life: number;
  maxLife: number;
  size: number;
  color: string;
  gravity: number;
}

const ClickExplosion = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particlesRef = useRef<Particle[]>([]);
  const { isPlaying, musicIntensity } = useMusicContext();
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    const createExplosion = (x: number, y: number) => {
      const particleCount = isPlaying ? 30 + Math.floor(musicIntensity * 20) : 20;
      const colors = resolvedTheme === 'dark' 
        ? ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
        : ['#E74C3C', '#3498DB', '#2ECC71', '#F39C12', '#9B59B6', '#1ABC9C', '#E67E22'];

      for (let i = 0; i < particleCount; i++) {
        const angle = (Math.PI * 2 * i) / particleCount;
        const velocity = 2 + Math.random() * 8;
        const intensity = isPlaying ? musicIntensity || 1 : 1;
        
        particlesRef.current.push({
          x,
          y,
          vx: Math.cos(angle) * velocity * intensity,
          vy: Math.sin(angle) * velocity * intensity,
          life: 1,
          maxLife: 60 + Math.random() * 40,
          size: 2 + Math.random() * 4 * intensity,
          color: colors[Math.floor(Math.random() * colors.length)],
          gravity: 0.1 + Math.random() * 0.1
        });
      }

      // Add some special effects for music mode
      if (isPlaying) {
        // Create ring explosion
        for (let i = 0; i < 12; i++) {
          const angle = (Math.PI * 2 * i) / 12;
          particlesRef.current.push({
            x,
            y,
            vx: Math.cos(angle) * 15,
            vy: Math.sin(angle) * 15,
            life: 1,
            maxLife: 30,
            size: 8,
            color: '#FFD700',
            gravity: 0
          });
        }
      }
    };

    const handleClick = (e: MouseEvent) => {
      createExplosion(e.clientX, e.clientY);
    };

    const handleTouch = (e: TouchEvent) => {
      e.preventDefault();
      for (let i = 0; i < e.touches.length; i++) {
        const touch = e.touches[i];
        createExplosion(touch.clientX, touch.clientY);
      }
    };

    // Add event listeners
    window.addEventListener('click', handleClick);
    window.addEventListener('touchstart', handleTouch);

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Update and draw particles
      particlesRef.current = particlesRef.current.filter(particle => {
        // Update physics
        particle.x += particle.vx;
        particle.y += particle.vy;
        particle.vy += particle.gravity;
        particle.life -= 1 / particle.maxLife;
        
        // Bounce off edges
        if (particle.x < 0 || particle.x > canvas.width) {
          particle.vx *= -0.8;
          particle.x = Math.max(0, Math.min(canvas.width, particle.x));
        }
        if (particle.y > canvas.height) {
          particle.vy *= -0.6;
          particle.y = canvas.height;
        }

        // Apply friction
        particle.vx *= 0.99;
        particle.vy *= 0.99;

        if (particle.life <= 0) return false;

        // Draw particle
        ctx.save();
        ctx.globalAlpha = particle.life;
        
        // Create gradient for each particle
        const gradient = ctx.createRadialGradient(
          particle.x, particle.y, 0,
          particle.x, particle.y, particle.size
        );
        gradient.addColorStop(0, particle.color);
        gradient.addColorStop(1, 'transparent');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();

        // Add glow effect
        if (isPlaying) {
          ctx.shadowBlur = particle.size * 2;
          ctx.shadowColor = particle.color;
          ctx.fill();
        }

        // Draw sparkle effect for special particles
        if (particle.color === '#FFD700') {
          ctx.fillStyle = '#FFFFFF';
          ctx.fillRect(particle.x - 1, particle.y - 1, 2, 2);
          ctx.fillRect(particle.x - 3, particle.y, 6, 1);
          ctx.fillRect(particle.x, particle.y - 3, 1, 6);
        }

        ctx.restore();
        return true;
      });

      requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      window.removeEventListener('click', handleClick);
      window.removeEventListener('touchstart', handleTouch);
    };
  }, [mounted, isPlaying, musicIntensity, resolvedTheme]);

  if (!mounted) return null;

  return (
    <>
      <canvas
        ref={canvasRef}
        className="fixed inset-0 pointer-events-none z-40"
        style={{ background: 'transparent' }}
      />
      
      {/* Click instruction */}
      <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 pointer-events-none">
        <div className="bg-black/70 text-white px-4 py-2 rounded-full text-sm font-mono animate-bounce">
          ✨ Click anywhere for explosions! ✨
        </div>
      </div>
    </>
  );
};

export default ClickExplosion;
