"use client";

import { useEffect, useRef, useState } from 'react';
import { useMusicContext } from './MusicContext';
import { useTheme } from 'next-themes';

const MatrixRain = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { isPlaying, musicIntensity } = useMusicContext();
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const dropsRef = useRef<number[]>([]);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Matrix characters - mix of code symbols and Japanese katakana
    const matrixChars = "アァカサタナハマヤャラワガザダバパイィキシチニヒミリヰギジヂビピウゥクスツヌフムユュルグズブヅプエェケセテネヘメレヱゲゼデベペオォコソトノホモヨョロヲゴゾドボポヴッン0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_+-=[]{}|;:,.<>?";
    const chars = matrixChars.split("");

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      
      const columns = Math.floor(canvas.width / 20);
      dropsRef.current = new Array(columns).fill(1);
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Detect typing
    let typingTimer: NodeJS.Timeout;
    const handleKeyPress = () => {
      setIsTyping(true);
      clearTimeout(typingTimer);
      typingTimer = setTimeout(() => setIsTyping(false), 1000);
    };

    window.addEventListener('keydown', handleKeyPress);

    const draw = () => {
      // Create trailing effect
      ctx.fillStyle = resolvedTheme === 'dark' 
        ? 'rgba(0, 0, 0, 0.04)' 
        : 'rgba(255, 255, 255, 0.04)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Set text properties
      ctx.fillStyle = resolvedTheme === 'dark' ? '#0F0' : '#00AA00';
      ctx.font = '15px monospace';

      // Calculate intensity multiplier
      let intensityMultiplier = 1;
      if (isPlaying) {
        intensityMultiplier = 1 + (musicIntensity || 1) * 2;
      }
      if (isTyping) {
        intensityMultiplier *= 3;
      }

      // Draw matrix rain
      for (let i = 0; i < dropsRef.current.length; i++) {
        // Random character
        const char = chars[Math.floor(Math.random() * chars.length)];
        
        // Color variations based on music
        if (isPlaying) {
          const hue = (Date.now() * 0.1 + i * 10) % 360;
          ctx.fillStyle = `hsl(${hue}, 100%, 50%)`;
        } else {
          ctx.fillStyle = resolvedTheme === 'dark' ? '#0F0' : '#00AA00';
        }

        // Add glow effect when music is playing
        if (isPlaying || isTyping) {
          ctx.shadowBlur = 10 * intensityMultiplier;
          ctx.shadowColor = ctx.fillStyle as string;
        } else {
          ctx.shadowBlur = 0;
        }

        // Draw character
        ctx.fillText(char, i * 20, dropsRef.current[i] * 20);

        // Reset drop randomly or when it reaches bottom
        if (dropsRef.current[i] * 20 > canvas.height && Math.random() > 0.975) {
          dropsRef.current[i] = 0;
        }

        // Move drop down with intensity-based speed
        dropsRef.current[i] += Math.random() * intensityMultiplier;
      }
    };

    const animate = () => {
      draw();
      requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      window.removeEventListener('keydown', handleKeyPress);
      clearTimeout(typingTimer);
    };
  }, [mounted, isPlaying, musicIntensity, resolvedTheme, isTyping]);

  if (!mounted) return null;

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-10"
      style={{ 
        opacity: isPlaying || isTyping ? 0.6 : 0.2,
        transition: 'opacity 0.5s ease'
      }}
    />
  );
};

export default MatrixRain;
