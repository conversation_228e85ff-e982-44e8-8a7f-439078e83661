"use client";

import { useState, useEffect } from 'react';
import { Music, X, ExternalLink } from 'lucide-react';

interface FloatingMusicButtonProps {
  playlistId?: string;
  playlistName?: string;
  delay?: number;
}

export default function FloatingMusicButton({ 
  playlistId = "37i9dQZF1DXcBWIGoYBM5M", // Replace with your actual playlist ID
  playlistName = "Arkit's Coding Vibes",
  delay = 5000 
}: FloatingMusicButtonProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    // Check if user has already dismissed it in this session
    const dismissed = sessionStorage.getItem('music-button-dismissed');
    if (dismissed) {
      setIsDismissed(true);
      return;
    }

    // Show the button after delay
    const timer = setTimeout(() => {
      setIsVisible(true);
      // Auto-expand after showing
      setTimeout(() => setIsExpanded(true), 500);
      // Auto-collapse after a while
      setTimeout(() => setIsExpanded(false), 8000);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  const handlePlayPlaylist = () => {
    const spotifyUrl = `https://open.spotify.com/playlist/${playlistId}?utm_source=generator`;
    window.open(spotifyUrl, '_blank');
    
    // Collapse after clicking
    setIsExpanded(false);
  };

  const handleDismiss = () => {
    setIsVisible(false);
    setIsDismissed(true);
    sessionStorage.setItem('music-button-dismissed', 'true');
  };

  const handleToggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  if (isDismissed || !isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 md:bottom-6 md:right-6 z-40 animate-in slide-in-from-bottom-2 duration-700">
      <div className={`
        bg-gradient-to-r from-green-500 to-green-600
        text-white rounded-full shadow-lg hover:shadow-xl
        transition-all duration-300 ease-out
        ${isExpanded ? 'pr-3' : 'p-2'}
      `}>
        <div className="flex items-center">
          {/* Music Icon Button */}
          <button
            onClick={handleToggleExpand}
            className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-white/10 transition-colors relative"
          >
            <div className={`transition-transform duration-300 ${isExpanded ? 'music-pulse' : ''}`}>
              <Music className="w-4 h-4 animate-pulse" />
            </div>
            {/* Spotify indicator */}
            <div className="absolute -top-1 -right-1 w-2 h-2 bg-white rounded-full"></div>
          </button>

          {/* Expanded Content */}
          <div className={`
            overflow-hidden transition-all duration-300 ease-out
            ${isExpanded ? 'max-w-xs opacity-100 ml-2' : 'max-w-0 opacity-0 ml-0'}
          `}>
            <div className="flex items-center justify-between min-w-0">
              <div className="min-w-0 flex-1">
                <p className="text-xs font-medium truncate">
                  {playlistName}
                </p>
                <p className="text-xs text-green-100 truncate">
                  Open in Spotify
                </p>
              </div>

              <div className="flex items-center space-x-1 ml-2 flex-shrink-0">
                <button
                  onClick={handlePlayPlaylist}
                  className="p-1 hover:bg-white/10 rounded-full transition-colors"
                  title="Open in Spotify"
                >
                  <ExternalLink className="w-3 h-3" />
                </button>
                <button
                  onClick={handleDismiss}
                  className="p-1 hover:bg-white/10 rounded-full transition-colors"
                  title="Dismiss"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
