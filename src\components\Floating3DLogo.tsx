"use client";

import { useRef, useEffect, useState } from 'react';
import { useMusicContext } from './MusicContext';
import { useTheme } from 'next-themes';

const Floating3DLogo = () => {
  const logoRef = useRef<HTMLDivElement>(null);
  const { isPlaying, musicIntensity } = useMusicContext();
  const { resolvedTheme } = useTheme();
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const handleMouseMove = (e: MouseEvent) => {
      setMousePos({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [mounted]);

  useEffect(() => {
    if (!logoRef.current || !mounted) return;

    const logo = logoRef.current;
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;
    
    // Calculate rotation based on mouse position
    const rotateX = (mousePos.y - centerY) / 20;
    const rotateY = (mousePos.x - centerX) / 20;
    
    // Add music reactive effects
    const musicRotation = isPlaying ? Math.sin(Date.now() * 0.005) * 10 : 0;
    const musicScale = isPlaying ? 1 + (musicIntensity || 1) * 0.1 : 1;
    
    logo.style.transform = `
      perspective(1000px) 
      rotateX(${rotateX + musicRotation}deg) 
      rotateY(${rotateY + musicRotation}deg) 
      scale(${musicScale})
    `;
  }, [mousePos, isPlaying, musicIntensity, mounted]);

  if (!mounted) return null;

  return (
    <div className="fixed top-8 right-8 z-50 pointer-events-none">
      <div
        ref={logoRef}
        className={`
          w-16 h-16 relative transition-all duration-300 ease-out
          ${isPlaying ? 'animate-pulse' : ''}
        `}
        style={{
          transformStyle: 'preserve-3d',
          filter: isPlaying ? 'drop-shadow(0 0 20px rgba(74, 222, 128, 0.6))' : 'none'
        }}
      >
        {/* 3D Cube Faces */}
        <div className="absolute inset-0 transform-gpu">
          {/* Front Face */}
          <div 
            className={`
              absolute inset-0 rounded-lg border-2 flex items-center justify-center font-bold text-lg
              ${resolvedTheme === 'dark' 
                ? 'bg-gradient-to-br from-green-400 to-blue-500 border-green-400 text-white' 
                : 'bg-gradient-to-br from-blue-500 to-purple-600 border-blue-500 text-white'
              }
              ${isPlaying ? 'music-reactive-glow' : ''}
            `}
            style={{ transform: 'translateZ(8px)' }}
          >
            A
          </div>
          
          {/* Back Face */}
          <div 
            className={`
              absolute inset-0 rounded-lg border-2 flex items-center justify-center font-bold text-lg
              ${resolvedTheme === 'dark' 
                ? 'bg-gradient-to-br from-purple-400 to-pink-500 border-purple-400 text-white' 
                : 'bg-gradient-to-br from-red-500 to-orange-600 border-red-500 text-white'
              }
            `}
            style={{ transform: 'translateZ(-8px) rotateY(180deg)' }}
          >
            K
          </div>
          
          {/* Right Face */}
          <div 
            className={`
              absolute inset-0 rounded-lg border-2 flex items-center justify-center font-bold text-lg
              ${resolvedTheme === 'dark' 
                ? 'bg-gradient-to-br from-yellow-400 to-red-500 border-yellow-400 text-white' 
                : 'bg-gradient-to-br from-green-500 to-teal-600 border-green-500 text-white'
              }
            `}
            style={{ transform: 'rotateY(90deg) translateZ(8px)' }}
          >
            💻
          </div>
          
          {/* Left Face */}
          <div 
            className={`
              absolute inset-0 rounded-lg border-2 flex items-center justify-center font-bold text-lg
              ${resolvedTheme === 'dark' 
                ? 'bg-gradient-to-br from-indigo-400 to-purple-500 border-indigo-400 text-white' 
                : 'bg-gradient-to-br from-purple-500 to-pink-600 border-purple-500 text-white'
              }
            `}
            style={{ transform: 'rotateY(-90deg) translateZ(8px)' }}
          >
            🚀
          </div>
          
          {/* Top Face */}
          <div 
            className={`
              absolute inset-0 rounded-lg border-2 flex items-center justify-center font-bold text-lg
              ${resolvedTheme === 'dark' 
                ? 'bg-gradient-to-br from-cyan-400 to-blue-500 border-cyan-400 text-white' 
                : 'bg-gradient-to-br from-yellow-500 to-orange-600 border-yellow-500 text-white'
              }
            `}
            style={{ transform: 'rotateX(90deg) translateZ(8px)' }}
          >
            ⭐
          </div>
          
          {/* Bottom Face */}
          <div 
            className={`
              absolute inset-0 rounded-lg border-2 flex items-center justify-center font-bold text-lg
              ${resolvedTheme === 'dark' 
                ? 'bg-gradient-to-br from-pink-400 to-red-500 border-pink-400 text-white' 
                : 'bg-gradient-to-br from-indigo-500 to-purple-600 border-indigo-500 text-white'
              }
            `}
            style={{ transform: 'rotateX(-90deg) translateZ(8px)' }}
          >
            🔥
          </div>
        </div>
        
        {/* Floating particles around the logo */}
        {isPlaying && (
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className="absolute w-2 h-2 bg-green-400 rounded-full animate-ping"
                style={{
                  top: `${20 + Math.sin(Date.now() * 0.01 + i) * 30}%`,
                  left: `${20 + Math.cos(Date.now() * 0.01 + i) * 30}%`,
                  animationDelay: `${i * 0.2}s`,
                  animationDuration: '2s'
                }}
              />
            ))}
          </div>
        )}
      </div>
      
      {/* Tooltip */}
      <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-center opacity-70 pointer-events-none">
        <div className={`px-2 py-1 rounded ${resolvedTheme === 'dark' ? 'bg-black/70 text-white' : 'bg-white/70 text-black'}`}>
          {isPlaying ? '🎵 Vibing' : '💫 Arkit'}
        </div>
      </div>
    </div>
  );
};

export default Floating3DLogo;
